// Run this book before test run

before(() => {
  const testFilePath = Cypress.spec.relative;
  if (testFilePath.includes("get-encoded-pin")) {
    console.log("Do nothing");
  } 
  else {
    if (testFilePath.includes("api-wave-pay")) {
      cy.loginWavePay();
    } else if (testFilePath.includes("api-merchant")) {
      cy.loginMerchant();
    }
    else {
      cy.loginWaveChannel();
    }
  }
});