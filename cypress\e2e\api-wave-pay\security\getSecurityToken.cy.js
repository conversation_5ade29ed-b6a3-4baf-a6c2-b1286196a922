const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe('Get Security Token', () => {

  it("To test security-sys/get-security-token return 200 success", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
        cy.request({
          method: "GET",
          url: `${ENDPOINTS.appUrl}/security-sys/get-security-token`,
          headers: {
            "wmt-mfs": respHeader["wmt-mfs"],
            "appId": HEADERS.appId,
            "appVersion": HEADERS.appVersion,
            "versionCode": HEADERS.versionCode,
            "deviceId": HEADERS.deviceId
          }
        }).then(resp => {
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
          expect(resp.status).to.equal(200);
          expect(resp.body).to.have.property("securityCounter");
          expect(typeof resp.body.securityCounter).to.equal("string");
        });
      });
    });
  });
