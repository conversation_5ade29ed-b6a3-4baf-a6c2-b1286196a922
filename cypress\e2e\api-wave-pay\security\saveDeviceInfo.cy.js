const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe('Save Device Info', () => {
  it("should successfully save device notification info", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
      cy.fixture("saveDeviceInfo.json").then((payload) => {
        cy.request({
          method: "POST",
          url: `${ENDPOINTS.appUrl}/security-sys/save-device-info`,
          failOnStatusCode: false,
          headers: {
            "wmt-mfs": respHeader["wmt-mfs"],
            "appId": HEADERS.appId,
            "appVersion": HEADERS.appVersion,
            "versionCode": HEADERS.versionCode,
            "deviceId": HEADERS.deviceId,
            "content-type": "application/json"
          },
          body: payload.success_200_no_token
        }).then(resp => {
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
          expect(resp.status).to.equal(200);
          
          // Verify response contains the success message
          expect(resp.body).to.equal("Notification Token inserted successfully.");
        });
      });
    });
  });
}); 