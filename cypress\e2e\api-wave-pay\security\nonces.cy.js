const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");
const deviceId = HEADERS.deviceId;
describe('Security Nonces', () => {
  it("To test nonces endpoint return 200 success", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
      cy.fixture("nonces.json").then((payload) => {
        cy.request({
          method: "POST",
          url: `${ENDPOINTS.appUrl}/security-sys/nonces/${deviceId}`,
          failOnStatusCode: false,
          headers: {
            "wmt-mfs": respHeader["wmt-mfs"],
            "appId": HEADERS.appId,
            "appVersion": HEADERS.appVersion,
            "versionCode": HEADERS.versionCode,
            "deviceId": HEADERS.deviceId,
            "content-type": "application/json"
          },
          body: payload.success_200
        }).then(resp => {
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
          expect(resp.status).to.equal(200);
          
          const testData = resp.body;
          expect(testData).to.have.property("message");
          expect(testData.message).to.equal("nonce creation successful");
          expect(testData).to.have.property("statusCode");
          expect(testData.statusCode).to.equal("SC000");
          expect(testData).to.have.property("respTime");
        });
      });
    });
  });
}); 