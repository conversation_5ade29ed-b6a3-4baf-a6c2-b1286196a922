{"predicates": [{"and": [{"equals": {"method": "POST", "path": "/security-sys/save-device-id"}}, {"matches": {"body": {"msisdn": "^[a-zA-Z0-9\\d_*-.]*$"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json", "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0.example-token"}, "body": {"responseMap": {}, "message": "The device id was added successfully"}}}]}