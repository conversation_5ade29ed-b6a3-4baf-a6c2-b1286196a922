import "./commands";
import "./auth";
import "cypress-mochawesome-reporter/register";
import crypto from "crypto-js";

const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");
const msisdn = Cypress.env("msisdn");

// For multipart/form-data
Cypress.Commands.add("form_request", (method, url, formData, done) => {
  cy.fixture("responseHeader.json").then((respHeader) => {
    const xhr = new XMLHttpRequest();
    xhr.open(method, url);
    xhr.setRequestHeader("wmt-mfs", respHeader["wmt-mfs"]);
    xhr.setRequestHeader("appId", HEADERS.appId);
    xhr.setRequestHeader("appVersion", HEADERS.appVersion);
    xhr.setRequestHeader("versionCode", HEADERS.versionCode);
    xhr.setRequestHeader("deviceId", HEADERS.deviceId);
    xhr.onload = function () {
      done(xhr);
    };
    xhr.onerror = function () {
      done(xhr);
    };
    xhr.send(formData);
  });
});

Cypress.Commands.add("generateQR", () => {
  console.log(Cypress.env("ENV"));
  if (Cypress.env("ENV") === "QA") {
    console.log("test12345");
  }
  cy.getAccessTokenExternal().then(resp => {
    expect(resp.status).to.equal(200);
    expect(resp.body).to.to.have.property("access_token");
    cy.fixture("qrPay.json").then((qrPay) => {
      cy.request({
        method: "POST",
        url: `${ENDPOINTS.baseUrl}/qr/v1/generate`,
        failOnStatusCode: false,
        headers: {
          "Authorization": resp.body.access_token
        },
        body: qrPay.generateQR
      }).then(resp => {
        expect(resp.status).to.equal(200);
        expect(resp.body.responseMap).to.have.property("qrString");
      });
    });
  });
});

Cypress.Commands.add("generateDynamicQR", () => {
  cy.getQRAccessTokenExternal().then(resp => {
    expect(resp.status).to.equal(200);
    expect(resp.body).to.to.have.property("access_token");
    cy.fixture("qrPay.json").then((qrPay) => {
      cy.request({
        method: "POST",
        url: `${ENDPOINTS.baseUrl}/qr/v1/mmqr/dynamic`,
        headers: {
          "Authorization": resp.body.access_token
        },
        body: qrPay.generateDynamicQR
      }).then(resp => {
        expect(resp.status).to.equal(200);
        expect(resp.body.responseMap).to.have.property("qrString");
      });
    });
  });
});

Cypress.Commands.add("parseQR", (qrString) => {
  cy.readFile("cypress/fixtures/responseHeader.json").then((respHeader) => {
    cy.request({
      method: "POST",
      url: `${ENDPOINTS.baseUrl}/${ENDPOINTS.v2Url}/parse-qr`,
      headers: {
        "wmt-mfs": respHeader["wmt-mfs"],
        "appId": HEADERS.appId,
        "appVersion": HEADERS.appVersion,
        "versionCode": HEADERS.versionCode,
        "deviceId": HEADERS.deviceId,
      },
      body: {
        "qrString": qrString
      }
    });
  });
});

Cypress.Commands.add("getHashNewPayWithWave", (data) => {
  var hash = crypto.HmacSHA256(data, Cypress.env("npwwSecret"));
  hash = hash.toString(crypto.enc.Hex);
  return hash;
});

Cypress.Commands.add("clickPinNPWW", () => {
  const pin = Cypress.env("pinPhone");
  for (var i = 0; i < pin.length; i++) {
    var digit = pin.charAt(i);
    cy.get(".pin-" + digit).click();
  }
});

Cypress.Commands.add("appendTransactionIdToFile", (transactionId) => {
  const filePath = "cypress/fixtures/transaction.json";
  cy.writeFile(filePath, transactionId + "\n", { flag: "a+" }); // Append with a newline
  cy.log(`Appended transactionId to ${filePath}`);
});

Cypress.Commands.add("appendTransactionIdToFile", (transactionId) => {
  const filePath = "cypress/fixtures/transaction.json";
  cy.writeFile(filePath, transactionId + "\n", { flag: "a+" }); // Append with a newline
  cy.log(`Appended transactionId to ${filePath}`);
});

Cypress.Commands.overwrite("log", (subject, message) => cy.task("log", message));


Cypress.Commands.add("getWalletBalance", () => {
  let balance;
  cy.readFile("cypress/fixtures/responseHeader.json").then((respHeader) => {
    cy.request({
      method: "GET",
      url: `${ENDPOINTS.appUrl}/${ENDPOINTS.v2Url}/wallet-balance`,
      failOnStatusCode: false,
      headers: {
        "wmt-mfs": respHeader["wmt-mfs"],
        "appId": HEADERS.appId,
        "appVersion": HEADERS.appVersion,
        "versionCode": HEADERS.versionCode,
        "deviceId": HEADERS.deviceId
      },
      body: {
        "msisdn": msisdn
      }
    }).then(resp => {
      expect(resp.status).to.equal(200);
      expect(resp.body.responseMap).to.have.property("balance");
      expect(resp.body.responseMap.balance).to.be.a("number");
      balance = resp.body.responseMap.balance;
      return balance;
    });
  });
});

Cypress.Commands.add("getNonceExpirationTime", () => {
  const expirationTime = new Date();
  expirationTime.setMinutes(expirationTime.getMinutes() + 1);
  return expirationTime.toISOString().split('.')[0]; //Converts it to ISO string format and removes milliseconds
});