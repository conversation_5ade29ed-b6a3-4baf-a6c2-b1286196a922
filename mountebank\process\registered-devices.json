{
    "predicates": [
        {
            "and": [
                {
                    "matches": {
                        "method": "GET",
                        "path": "/security-sys/registered-devices",
                        "headers": {
                            "content-type": "application/json"
                        }
                    }
                },
                {
                    "exists": {
                        "headers":{
                            "wmt-mfs": true,
                            "deviceid": true,
                            "appversion": true,
                            "versionCode": true
                        }
                    }
                }
            ]
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "headers": {
                    "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2Iiwia2lkIjoibWZzIiwiY3R5IjoiSldUIn0.nmA03poWKlA_v8i8qq2G4pYAGcsI2GHNyQgscNmOs_RlENyJneXxcAn-ARqT8COhOJNdt-QVK3WQsvz2eXHkmvZ9-mwT8MmevNhO1-6qsPoWkkcuLQ4H7aUhMOrCOniCLiO6VPoeCjyQJH2uqOf8KWhEBOfoDkF9B6K4S5GDhzWNAU3yI0X2Krfb8KqUetSnmhm-y5Jgg_0Aqp5CjpO58E95iI6tjvvw3K3SbDg0_Vu2yHT4msa018Hu5v6Kp_6FZ1RSVhwPIPlo-oh7qQcMHD3wcv06t8jmd1euxFHAQCe04-R3RjF7ZFUKDfWAXN50owKQk1DtHjMbji4_r9j4vQ.Zigd2a3Q5WichP0YD5ahBg.4jHtkDPUQPJa3oQIMGFhrVacQsqC1ygyD3-NBLHQha-S78NV1TyDCm2DBpuhS6SMvcvG82jC7BPTObG0sog_aENtZFHaJ8SN9l38oq2CMcAfV1h_SYkyIvbNqyfv_Ockp7ikgakax0waFEsZTPKqUJvNO0uFJXUOJ0b-7MwKoGsVFtE7JYprQJJqXdwJgA6D1hsTunt3claUvJnB5WIGMQb7rntAWHkL1V_aQDHju_gRoGV2M_s7USNqM3QhRifznwXm9I_nTaMIW2xe31UE4OCtOwRmL0LxQIHDDDPgaEdfSdykPKIV1d5bsdmyIPHTBBaGqsQjEdrKc09ItfL7_Znjgs9n8wU2kNxdtohWjne231vbLtK99G_N1_CNphjIypzelyaOBDrj0RXi9p0Z137ChH0L8ILQme1TZqxmO5DNMvx3AXoYFAS4PegvHsi5xDygWrkDqajNlQWvlfMuQ_Mylj-z17I9s883bvGL1JMtU_AIWKiG3RNeA9IYMJAUmHifIL25WNN9Wy700WyB_iNLcTQRCTDpooQuNv8BLJnNaefqCHlRiA-b-RSHbecWQ6YgeO80Won8dEpnVIoN0SA_tCvf6yfIM9SJWQmSUuoX0lfhS25JqIAr9kd3INJHfOBU0WtWgDe-RFsqk62U-vc5DHr0XRnjkxPcoBcMookEDmng5sMX9enuwOadD2mw6GPjwi7WNxORBK72SpzHGzMeIVjWbihDPEnP6a4GzG4mVruGqBgVZsV2-ZN7bnt1xtbFtk3aAGx3rzPEH74M3dyynduxcuzHAHvdbJulmiajlvqhP_RozH5T1P0kDpViIqM5-VtOK-2WIevF7OzPr_9MaNvoASKZRg5AW34thxB5sdsTk6PF7TK_OPm4VHm7_8DTdFp6oPomUEGTMDfRwHNYa9WmnAzIfkg720I5PE9HU8KAk4fDSihx4yWBDTG9Q7ZjIvdkvWMxy96e_ni_35I5gJuMTVEqoSIiQ9YgKgiL0kAr0SwGNhucZcs-3ThJu-2D6w8Ln7Dj2DO1Gci2UisiDhKIe3a8AhvoVrKg4A7KX68ODj1xm-4FNBgnIpjCugCKojNCNsy6VGWT2BG7NV5WGQEzpQLaFnTBgtwhgyrNJ1INTPGo4ZkRNC70cM4J-V7bKLvv6YFY4M6NOfTaSqtbTtuqphrhXTCl3a_LvS8_Iz0CSzj7vPT5FQ6qrfZrioc5nmPCnXtED9La7Hzuf3pN48-Ckm9InkSzptMBsrOR_-uJhsvGV09EDqAvlnRHJM1iLb8bCtRh65T9e2WHO-XfanuNiSfHVKrmOTGtT_SfA1HcjHIF9MIl907KoItWF41YTlEOx2LuaKYv99N652UtwaFE9f6m8gdQ5Ax9q2JRjo9_P7-4_KLFbv2LzwbV9mu76qyiCGwLEliWD6lR8b7k61CIJ6vU7apzuLU5g0mHeo6niI1wYBur6MO26NKde7jX8jTKNn5TU_nbpyJMDAoF8uzy9fpV9bIMwvNUa4lcbWYkKols-X7gL-Ygy3yQ814mKo_ITaZU3rIxnTQUx55VwLmFD7Ofi_fYt5P5QRk.1qAdfhGu1y15FasE1CpO0Q"
                },
                "body": {
                    "responseMap": {
                        "registeredDevices": [
                            {
                                "deviceId": "1bbfd51bb8021047c24ea3bf20d70f76a286f2a8",
                                "deviceAuthorizationStatus": "0",
                                "msisdn": "9450340223",
                                "cpiAbi": "x86_64,arm64-v8a",
                                "manufacturer": "Google",
                                "model": "sdk_gphone64_x86_64",
                                "product": "sdk_gphone64_x86_64",
                                "osVersion": "14",
                                "appVersion": "2.3.1",
                                "versionCode": 1463,
                                "device": "sdk_gphone64_x86_64(sdk_gphone64_x86_64)",
                                "createdDate": "2025-02-21T12:03:49.000000",
                                "lastModifiedDate": "2025-02-21T12:32:10.000000",
                                "appId": "mm.com.wavemoney.wavepay.newSit",
                                "notificationToken": "duPDjDjJR3GAesuS_KHpt6:APA91bH8KN5WaFb5a3fK8_DWEwMNrpORBdtfd5CzdzZBy4Bl5OIFXzXCpku6C7QSo5_7ZsElUov6fnJE1lg5NzwaFhC-_NwQmNS3gnLmnVvlZFvRPbMmiEs",
                                "allowed": false
                            },
                            {
                                "deviceId": "fab08010db1b2eaf2ebb0656488c8183a2c213c0",
                                "deviceAuthorizationStatus": "0",
                                "msisdn": "9450340223",
                                "cpiAbi": "x86_64,arm64-v8a",
                                "manufacturer": "Google",
                                "model": "sdk_gphone64_x86_64",
                                "product": "sdk_gphone64_x86_64",
                                "osVersion": "14",
                                "appVersion": "2.3.1",
                                "versionCode": 1463,
                                "device": "sdk_gphone64_x86_64(sdk_gphone64_x86_64)",
                                "createdDate": "2025-02-21T11:48:29.000000",
                                "lastModifiedDate": "2025-02-21T11:54:22.000000",
                                "appId": "mm.com.wavemoney.wavepay.newSit",
                                "notificationToken": "dr91THaSTcGLguycimHB_2:APA91bHTgsw3Zgzgs_gKto4QcWkTfZTyEbJ3U7tjJBcu2WX_vzrW1B-GagWhntI7xxrTsFADrIMcsbXOq8PqFlXjlgqX6e8Z2NCj49uqgF5Xl6E71LTXn5Q",
                                "allowed": false
                            }
                        ]
                    }
                }
            }
        }
    ]
},
{
    "predicates": [
        {
            "and": [
                {
                    "equals": {
                        "method": "PATCH",
                        "path": "/security-sys/registered-devices"
                    }
                },
                {
                    "exists": {
                        "body": {
                            "deviceAuthorizationStatus": "^[a-zA-Z\\d!@#$% &*/]*$",
                            "pin": "^[a-zA-Z\\d!@#$% &*/]*$",
                            "deviceIdToPatch": "^[a-zA-Z\\d!@#$% &*/]*$"
                        }
                    }
                }
            ]
        }
    ],
    "responses": [
        {
            "inject": "<%- stringify(filename, 'js/registered-devices.js') %>"
        }
    ]
}