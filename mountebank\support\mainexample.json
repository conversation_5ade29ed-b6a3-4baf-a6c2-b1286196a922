{
  "predicates": [{
    "matches": {
      "method": "GET",
      "path": "/service/otp/(0?97[4-9]\\d{7})$"
    }
  }],
  "responses": [
    {
      "inject": "<%- stringify(filename, 'js/randomotp.js') %>"
    }
  ]
},
{
  "responses": [
    {
    "is": {
      "statusCode": 404,
      "headers": {
        "Content-Type": "application/json"
      },
      "body": {
        "message": "Error 404 File not found"
      }
    }
  }],
  "predicates":[
    {
      "and":[
        {"equals": {"method": "GET"}},
        {"not": {"startsWith":{"path": "/service/otp"}}}
      ]
    }
  ]
}