const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe('WavePay Security Token', () => {
  it("should successfully retrieve wavepay security token", () => {    
    cy.fixture("responseHeader.json").then((respHeader) => {
        cy.request({
          method: "GET",
          url: `${ENDPOINTS.appUrl}/security-sys/get-wavepay-security-token`,
          failOnStatusCode: false,
          headers: {
            "appId": HEADERS.appId,
            "appVersion": HEADERS.appVersion,
            "versionCode": HEADERS.versionCode,
            "deviceId": HEADERS.deviceId
          }
        }).then(resp => {
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
          expect(resp.status).to.equal(200);
          
          expect(resp.body).to.have.property("responseMap");
          expect(resp.body.responseMap).to.have.property("securityCounter");
          expect(resp.body.responseMap.securityCounter).to.be.a("string");
          expect(resp.body).to.have.property("message", "Optional message to show to user.");
          expect(resp.body).to.have.property("statusCode", "SC0000");
        });
    });
  });
}); 