{
    "predicates": [
        {
            "and": [
                {
                    "matches": {
                        "method": "POST",
                        "path": "/security-sys/forgot-pin",
                        "headers": {
                            "content-type": "application/json"
                        }
                    }
                },
                {
                    "equals": {
                        "body": {
                            "msisdn": "9123456791"
                        }
                    }
                }
            ]
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "PIN_IS_RESET"
                    },
                    "message": "Your PIN has been reset",
                    "respTime": "Dec 28, 2024 09:14:23 AM"
                }
            }
        }
    ]
},
{
    "predicates": [
        {
            "matches": {
                "method": "POST",
                "path": "/security-sys/forgot-pin",
                "headers": {    
                    "content-type": "application/json"
                }
            }
        },
        {
            "equals": {
                "body": {
                    "msisdn": "9978835353"
                }
            }       
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "VERIFY_NRC"
                    },
                    "message": "Please enter your NRC",
                    "respTime": "Dec 23, 2024 09:01:39 AM"
                }
            }
        }
    ]
},
{
    "predicates": [
        {
            "matches": {
                "method": "POST",   
                "path": "/security-sys/forgot-pin",
                "headers": {
                    "content-type": "application/json"
                }
            }
        },
        {
            "equals": {
                "body": {
                    "msisdn": "9793102312"
                }
            }
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "VERIFY_DOB"
                    },
                    "message": "Please enter your date of birth",
                    "respTime": "Dec 23, 2024 09:01:39 AM"
                }
            }
        }
    ]
}