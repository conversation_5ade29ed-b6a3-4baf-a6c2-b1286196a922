var request = JSON.parse(process.env.MB_REQUEST),
    response = JSON.parse(process.env.MB_RESPONSE),
    requestId = request.path.match(/\/service\/otp\/transformer\/(\d+)/),
    captureID = requestId ? requestId[1] : null,
    fs = require('fs'),
    mappings = fs.readFileSync('js/user.csv', { encoding: 'utf8' }),
    lines = mappings.split(/\r?\n/);

for (let i = 0; i < lines.length; i += 1) {
    var fields = lines[i].split('|'),
        id = fields[0],
        name = fields[1];

    if (captureID === id) {
        response.body = response.body.replace('${YOU}', name);
    }
}

console.log(JSON.stringify(response));