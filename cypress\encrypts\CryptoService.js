/* eslint-disable no-async-promise-executor */
/* eslint-disable no-undef */
const crypto = require("crypto");
const KeyProvider = require("./KeyProvider");

class CryptoService {
  encrypt(text, environment) {
    return new Promise(async (resolve, reject) => {
      try {
        const keyProvider = new KeyProvider();
        const publicKey = keyProvider.getPublicKey(environment);
                
        const buffer = Buffer.from(text, "utf8");
        const encrypted = crypto.publicEncrypt({
          key: publicKey,
          padding: crypto.constants.RSA_PKCS1_PADDING
        }, buffer);
        const encryptedBase64 = encrypted.toString("base64");
        resolve(encryptedBase64);
      } catch (error) {
        reject(error);
      }
    });
  }
}

module.exports = CryptoService;