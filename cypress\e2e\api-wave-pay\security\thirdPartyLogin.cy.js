const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe('Third Party Login Error Cases', () => {
  it("Verify GET Request on /security-sys/third-party-login Returns 405 Method Not Supported", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
        cy.request({
          method: "GET",
          url: `${ENDPOINTS.appUrl}/security-sys/third-party-login`,
          failOnStatusCode: false,
          headers: {
            "wmt-mfs": respHeader["wmt-mfs"],
            "appId": HEADERS.appId,
            "appVersion": HEADERS.appVersion,
            "versionCode": HEADERS.versionCode,
            "deviceId": HEADERS.deviceId,
            "content-type": "application/json"
          }
        }).then(resp => {
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
          expect(resp.status).to.equal(405);
          
          expect(resp.body).to.have.property("instance");
          expect(resp.body).to.have.property("detail");
          expect(resp.body).to.have.property("title");
          expect(resp.body).to.have.property("type");

          expect(resp.body.status).to.equal(405);
        });
    });
  });

}); 