services:
  mountebank:
    build:
      dockerfile: mountebank.dockerfile
    entrypoint: mb --configfile imposters.ejs --allowInjection
    networks:
      - mountebank

  cypress:
    build:
      dockerfile: cypress.dockerfile
    environment:
      - TEST_NAME=$TEST_NAME
    entrypoint: npm run cy:$TEST_NAME -- --env environmentName=mountebank && $?
    networks:
      - mountebank
    depends_on:
      - mountebank

networks:
  mountebank:
    driver: bridge
