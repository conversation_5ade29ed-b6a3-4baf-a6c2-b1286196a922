{"predicates": [{"matches": {"method": "PATCH", "path": "/security-sys/nonces/[A-Za-z0-9-_=]+.[A-Za-z0-9-_=]+.[A-Za-z0-9-_.+/=]*"}, "exists": {"headers": {"transactiontype": true, "msisdn": true, "deviceid": true, "to-include-device-info": true, "content-type": true, "wmt-mfs": true, "userType": true}}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json", "Vary": "Origin"}, "body": {"message": "nonce update successful", "statusCode": "SC000", "respTime": 1740709461851}}}]}