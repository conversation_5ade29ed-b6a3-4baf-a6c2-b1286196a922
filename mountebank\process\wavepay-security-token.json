{"predicates": [{"equals": {"method": "GET", "path": "/security-sys/get-wavepay-security-token"}}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json", "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0.example-token", "Vary": "Origin"}, "body": {"responseMap": {"securityCounter": "356b778f-02b1-45c1-9720-c87f4e2be814"}, "message": "Optional message to show to user.", "statusCode": "SC0000"}}}]}