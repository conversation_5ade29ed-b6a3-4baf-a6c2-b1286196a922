/* eslint-disable no-undef */
const fs = require("fs");
const path = require("path");
const crypto = require("crypto");

class KeyProvider {

  loadProperties() {
    try {
      const content = fs.readFileSync(path.resolve("cypress/encrypts/keys.properties"), { encoding: "utf-8" });
      const lines = content.split("\n");

      const properties = {};
      let currentKey = null;
      let currentValue = "";

      lines.forEach((line) => {
        // Skip empty lines and comments
        if (line.trim() === "" || line.trim().startsWith("#")) return;

        if (line.includes("=")) {
          // If we encounter a new key-value pair, store the current one and reset the accumulator
          if (currentKey !== null) {
            properties[currentKey] = currentValue.trim().replace(/\\$/gm, ""); // Remove line continuation characters
          }
          const [key, value] = line.split("=");
          currentKey = key.trim();
          currentValue = value;
        } else {
          // Continuation of the previous value
          currentValue += line;
        }
      });

      // Don't forget to add the last key-value pair
      if (currentKey !== null) {
        properties[currentKey] = currentValue.trim().replace(/\\$/gm, ""); // Remove line continuation characters
      }

      return properties;
    } catch (error) {
      console.error("Error reading properties file:", error);
      return {};
    }
  }

  getPublicKey(environment) {
    const publicKeyProperty = `${environment}.publicKey`;
    const properties = this.loadProperties();
    const publicKeyBase64 = properties[publicKeyProperty];

    if (!publicKeyBase64) {
      throw new Error(`${publicKeyProperty} is not defined in keys.properties file`);
    }

    const publicKeyBuffer = Buffer.from(publicKeyBase64, "base64");
    const publicKey = crypto.createPublicKey({
      key: publicKeyBuffer,
      format: "der",
      type: "spki"
    });

    return publicKey;
  }
}

module.exports = KeyProvider;
