const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe('Update nonce security token expiration time.', () => {
  it("To test nonces-update return 200 success", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
      cy.fixture("noncesSecurityToken.json").then((payload) => {
        cy.getSecurityToken().then(securityToken => {
          cy.getNonceExpirationTime().then(expirationTime => {
            cy.request({
              method: "PATCH",
              url: `${ENDPOINTS.appUrl}/security-sys/nonces/${securityToken}`,
              headers: {
                "transactiontype": payload.transactionType.update,
                "msisdn": HEADERS.msisdn,
                "deviceid": HEADERS.deviceId,
                "wmt-mfs": respHeader["wmt-mfs"],
              },
              body: {
                "expirationTime": expirationTime
              }
            }).then(resp => {
              cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
              
              // Validate response
              expect(resp.status).to.equal(200);
              expect(resp.body).to.have.property("message", "nonce update successful");
              expect(resp.body).to.have.property("statusCode", "SC000");
              expect(resp.body).to.have.property("respTime");
              expect(resp.body.respTime).to.be.a("number");
              
              // Validate headers
              expect(resp.headers).to.have.property("content-type").that.contains("application/json");
              expect(resp.headers).to.have.property("vary", "Origin");
            });
          });
        });
      });
    });
  });
}); 