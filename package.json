{"name": "api-automation", "version": "1.0.0", "description": "for API automation", "main": "index.js", "scripts": {"cy:open": "cypress open --config watchForFileChanges=false --env ENV=QA", "start": "mb restart --configfile mountebank/imposters.ejs --allowInjection --logfile mountebank/log/mb.log", "watch": "nodemon -e js,json,ejs --exec \"npm run start\"", "cy:runSecurity": "cypress run --spec 'cypress/e2e/api-wave-pay/security/**'"}, "repository": {"type": "git", "url": "***********************:mobile-test-automations/api-automation.git"}, "author": "QA Automation Team", "license": "ISC", "dependencies": {"cypress": "^12.17.3", "cypress-multi-reporters": "^2.0.4"}, "devDependencies": {"@faker-js/faker": "^8.4.1", "crypto-js": "^4.1.1", "cypress-mochawesome-reporter": "^3.2.2", "eslint": "^8.24.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-cypress": "^2.12.1", "mocha-junit-reporter": "^2.2.1", "nodemon": "^3.1.4"}}