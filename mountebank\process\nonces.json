{"predicates": [{"and": [{"matches": {"method": "POST", "path": "/security-sys/nonces/[^/]+"}}, {"matches": {"body": {"expirationTime": "^[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}$"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json", "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0.HZxDyoYRD2vHNKYdPzv5_KmN591bR-07cX_yDyCbpOG4aJFaeUYms8BygIlF_rbORqnbtS7i__sgBFoJ4uq8AvPSDTk1RYNsfuTGdBJsw60GWnhpVGJZsDPcMbeaBX-_BOJhhPgDIqlZyUck4fPQkakxNnOqOSq_3zF4OJAaYVyDm-VcziMXDq3cGZFp-dPgQpH5I6qrUWmX39v-dKOwfqd2USYwHcW_A8nszpSChG18IJDthK_eaPvFpEM7Tl--jYbDspJDLGD5i6Thu2HiQO_XJnFAH_AEO8NpUVGi4sEp9Uun8fgK-BItmJHBRlzOhK2LLfzZAtI826mhuGMjHQ", "Vary": "Origin"}, "body": {"message": "nonce creation successful", "statusCode": "SC000", "respTime": 1740709463533}}}]}