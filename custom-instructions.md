### Custom Instruction for Code Generation

#### Overview
You are an AI agent tasked with creating code for an API simulation and testing project that follows a consistent format. The project involves:
1. A Mountebank stub file used to mock API endpoints.
2. A Cypress fixture file containing test payloads.
3. A Cypress test file that sends API requests and validates the responses using assertions.

All endpoints, headers, and payloads must adhere to the sample format provided in the custom-request-response.md file. Your code must match these specifications exactly.

#### Input Data Format
You will receive API request and response samples in the custom-request-response.md file. Each case includes:

1. A request with:
   - HTTP method (typically POST, GET, PATCH, etc.)
   - Path (e.g., `/security-sys/register-customer`)
   - Headers (including content-type, appid, deviceid, etc.)
   - Request body (which may be form-urlencoded or JSON)

2. A response with:
   - Status code (200 for success, or appropriate error codes)
   - Headers
   - Response body with specific structure

#### Content-Type Handling

**Important:** There is a critical difference between the actual requests and how they should be mocked:

1. **Actual Requests:** Many actual API requests use `application/x-www-form-urlencoded` content-type with bodies like `msisdn=**********&countryCode=+95`

2. **Cypress Tests & Mountebank Mocks:** The content-type will be provided from the request. Some endpoints may use `application/x-www-form-urlencoded` while others may use `application/json` with JSON bodies like `{"msisdn": "**********", "countryCode": "+95"}`

#### Code Files to Generate

1. **Mountebank Stub File:**
   - **Location:** `mountebank/process/[endpoint-name].json`
   - **Format:**
     - An array of stub objects, each handling a specific test case.
     - Each stub must include:
       - **Predicates:**
         - An `and` array containing:
           - An `equals` predicate for method, path, and content-type header
           - An `equals` predicate to match the JSON body fields
       - **Responses:**
         - An object under `"is"` which includes:
           - `statusCode` (200 for success, or appropriate error code)
           - `headers` with at least `"Vary": "Origin"` and `"Content-Type": "application/json"`
           - `body` with the expected response structure

   **Example Stub Object:**

   ```json
   {
     "predicates": [
       {
         "and": [
           {
             "equals": {
               "method": "POST",
               "path": "/security-sys/register-customer",
               "headers": {
                 "content-type": "application/x-www-form-urlencoded"
               }
             }
           },
           {
             "equals": {
               "body": {
                 "msisdn": "**********",
                 "countryCode": "+95"
               }
             }
           }
         ]
       }
     ],
     "responses": [
       {
         "is": {
           "statusCode": 200,
           "headers": {
             "Vary": "Origin",
             "Content-Type": "application/json"
           },
           "body": {
             "responseMap": {},
             "respTime": "Feb 21, 2025 02:40:12 PM",
             "message": "Registration request successfully submitted.",
             "statusCode": "SC0000"
           }
         }
       }
     ]
   }
   ```

2. **Cypress Fixture File:**
   - **Location:** `cypress/fixtures/[endpoint-name].json`
   - **Contents:**
     - A JSON object that maps descriptive identifiers to request body payloads.
     - Each identifier should clearly indicate the test case purpose.

   **Example Fixture File:**

   ```json
   {
     "registerSuccess": {
       "countryCode": "+95",
       "msisdn": "**********"
     },
     "alreadyRegistered": {
       "msisdn": "**********"
     },
     "invalidNumber": {
       "msisdn": "**********",
       "countryCode": "+95"
     },
     "accountLocked": {
       "msisdn": "**********"
     }
   }
   ```

3. **Cypress Test File:**
   - **Location:** `cypress/e2e/api-wave-pay/security/[endpoint-name].cy.js` (This is just an example of the location. It will be depend on the project structure)
   - **Structure:**
     - Import environment variables for endpoints and headers
     - Define a describe block with the API name
     - Create it blocks for each test case
     - Use fixtures to load test data
     - Make requests with appropriate headers and body
     - Assert on response status and body properties

   **Example Test File:**

   ```javascript
   const ENDPOINTS = Cypress.env("endpoint");
   const HEADERS = Cypress.env("headers");

   describe("Register Customer API Testing", () => {
     it("To test successful registration request", () => {
       cy.fixture("registerCustomer.json").then((payload) => {
         cy.request({
           method: "POST",
           url: `${ENDPOINTS.appUrl}/security-sys/register-customer`,
           headers: {
             "content-type": "application/x-www-form-urlencoded",
             "appid": HEADERS.appId,
             "appVersion": HEADERS.appVersion,
             "versionCode": HEADERS.versionCode,
             "deviceid": HEADERS.deviceId
           },
           form: true,
           body: payload.registerSuccess
         }).then((response) => {
           expect(response.status).to.eq(200);
           expect(response.body).to.have.property("responseMap");
           expect(response.body).to.have.property("message", "Registration request successfully submitted.");
           expect(response.body).to.have.property("statusCode", "SC0000");
           expect(response.body).to.have.property("respTime");
         });
       });
     });

     // Additional test cases...
   });
   ```

#### Authentication and Headers

1. **Standard Headers for All Requests:**
   - `"content-type"` will be provided from the request (could be `"application/json"` or `"application/x-www-form-urlencoded"` depending on the endpoint)
   - `"appid": HEADERS.appId`
   - `"appVersion": HEADERS.appVersion`
   - `"versionCode": HEADERS.versionCode`
   - `"deviceid": HEADERS.deviceId`

2. **Authenticated Requests:**
   - For endpoints requiring authentication, include the `"wmt-mfs"` token from response headers:
   ```javascript
   cy.fixture("responseHeader.json").then((respHeader) => {
     cy.request({
       // ... other request properties
       headers: {
         "wmt-mfs": respHeader["wmt-mfs"],
         // ... other headers
       }
     })
   });
   ```

#### Response Structure Variations

Response structures vary by endpoint and status code. Common patterns include:

1. **Success Responses (200):**
   ```json
   {
     "responseMap": { /* may contain additional data */ },
     "respTime": "Feb 21, 2025 02:40:12 PM",
     "message": "Success message",
     "statusCode": "SC0000"
   }
   ```

2. **Error Responses:**
   ```json
   {
     "message": "Error message",
     "codeStatus": "18",
     "timestamp": "24-02-2025 09:23:54"
   }
   ```

#### Key Points for the AI Agent

1. **Content-Type Handling:**
   - Use the content-type provided in the request (could be `"application/json"` or `"application/x-www-form-urlencoded"`)
   - For form-urlencoded requests, use `form: true` in Cypress requests when appropriate
   - For JSON requests, provide the body as a JSON object

2. **Response Validation:**
   - Validate appropriate status codes (200 for success, or specific error codes)
   - Check for expected properties in the response body
   - Verify specific values where appropriate (messages, codes, etc.)
   - Always check for timestamp fields (respTime or timestamp)

3. **Fixture Organization:**
   - Create descriptive identifiers for each test case
   - Group related test cases in a single fixture file
   - Structure fixtures to match the expected request body format

4. **Mountebank Predicates:**
   - Use `equals` for exact matching of method, path, and headers
   - Use `equals` for body matching when the entire body must match
   - Use `matches` with regex when partial or pattern matching is needed
   - Always include content-type header in predicates

5. **Environment Variables:**
   - Use `ENDPOINTS.appUrl` for the base URL
   - Use `HEADERS` object for common headers
   - For authenticated endpoints, get the token from responseHeader.json

6. **Error Handling:**
   - Include tests for both success and error scenarios
   - Verify appropriate error messages and codes
   - Use appropriate status code expectations for different error types

7. **Form-Urlencoded Data Handling:**
   - When the request uses `application/x-www-form-urlencoded` with data like `msisdn=**********&countryCode=+95`
   - Use the `form: true` option in Cypress requests when the content-type is `application/x-www-form-urlencoded`
   - In Mountebank stubs, use the appropriate format based on the content-type in the request
   - For JSON requests, provide the body as a JSON object: `{ "msisdn": "**********", "countryCode": "+95" }`

8. **Authentication Flow:**
   - Many endpoints require authentication via the `wmt-mfs` token
   - This token is obtained from previous requests and stored in `cypress/fixtures/responseHeader.json`
   - For tests that require authentication, always load this token from the fixture
   - Some endpoints may require additional security tokens or encoded PINs
   - Use helper commands like `cy.getEncodePin()` when needed for secure values

9. **Password Hashing in Tests:**
   - When a test requires a password hash (for login, PIN verification, etc.), always use the `getEncodePin()` function
   - This function generates a secure, properly formatted password hash that works with the security microservice
   - The function handles retrieving a security token and encrypting the PIN/password with it
   - Usage example for agent login:
     ```javascript
     cy.getEncodePin().then((encryptedPassword) => {
       loginData.agentSuccessLogin200 = { ...loginData.agentSuccessLogin200, password: encryptedPassword };
       cy.request({
         method: "POST",
         url: `${ENDPOINTS.appUrl}/security-sys/agent-login`,
         headers: { /* headers */ },
         body: loginData.agentSuccessLogin200
       });
     });
     ```
   - For different user types, use the appropriate variant:
     - `cy.getEncodePin()` - Standard password hashing
     - `cy.getEncodePinMerchant()` - For merchant login
     - `cy.getEncodePinWaveChannel()` - For Wave Channel login
   - Never hardcode password hashes in test fixtures or Mountebank stubs
   - The encrypted password format in request/response files is for reference only

10. **Test Structure and Organization:**
    - Place all API tests in the `cypress/e2e/api-wave-pay/security/` directory (This is just an example of the location. It will be depend on the project structure)
    - Name test files after the endpoint they test (e.g., `registerCustomer.cy.js`)
    - Group related test cases within a single describe block
    - Use descriptive it block titles that clearly indicate the test purpose
    - Follow the pattern: "To test [scenario description]" for consistency
    - For each endpoint, include tests for both success and error cases
    - When appropriate, save response headers for use in subsequent tests:
      ```javascript
      cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);
      ```

11. **Response Status Code Handling:**
    - Use appropriate status codes based on the response type:
      - 200: Successful operations
      - 400: Bad request/validation errors
      - 401/403: Authentication/authorization errors
      - 409: Conflict (e.g., already registered)
      - 423: Locked resource
    - In Cypress tests, set expectations for the correct status code
    - In Mountebank stubs, return the appropriate status code