const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Registered Devices Testing", () => {
  it("Test resgistered devices success GET request", () => {
    cy.fixture("responseHeader.json").then((responseHeaders) => {
      cy.request({
        method: "GET",
        url: `${ENDPOINTS.appUrl}/security-sys/registered-devices`,
        headers: {
          "content-type": "application/json",
          "wmt-mfs": responseHeaders["wmt-mfs"],
          "deviceid": HEADERS.deviceId,
          "appVersion": HEADERS.appVersion,
          "versionCode": HEADERS.versionCode,
        },
      }).then((response) => {
        cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);
        expect(response.status).to.eq(200);
        
        // Parse response body if it's a string
        const responseBody = typeof response.body === 'string' ? JSON.parse(response.body) : response.body;
        
        expect(responseBody).to.have.property("responseMap");
        expect(responseBody.responseMap).to.have.property("registeredDevices");
        expect(responseBody.responseMap.registeredDevices).to.be.an("array");
        
        // Check if registered devices is empty or not
        expect(responseBody.responseMap.registeredDevices).to.have.length.above(0);
        
        // Get a random device from the array
        const randomIndex = Math.floor(Math.random() * responseBody.responseMap.registeredDevices.length);
        
        // Validate individual properties
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("deviceId");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("msisdn");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("cpiAbi");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("manufacturer");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("model");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("product");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("osVersion");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("appVersion");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("versionCode");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("device");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("createdDate");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("lastModifiedDate");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("appId");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("notificationToken");
        expect(responseBody.responseMap.registeredDevices[randomIndex]).to.have.property("allowed");
      });
    });
  });

  it("Test resgistered devices success PATCH request", () => {
    cy.fixture("responseHeader.json").then((responseHeaders) => {
      cy.fixture("registered-devices.json").then((payload) => {
        cy.getEncodePin().then(encodedPin => {
          payload.registeredDevicesPatch = { ...payload.registeredDevicesPatch, pin: encodedPin,
            deviceIdToPatch: HEADERS.deviceId};
          cy.request({
            method: "PATCH",
            url: `${ENDPOINTS.appUrl}/security-sys/registered-devices`,
            headers: {
              "content-type": "application/json",
              "wmt-mfs": responseHeaders["wmt-mfs"],
              "deviceid": HEADERS.deviceId,
              "appVersion": HEADERS.appVersion,
              "versionCode": HEADERS.versionCode,
            },
            body: payload.registeredDevicesPatch
          }).then((response) => {
            cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);
            expect(response.status).to.eq(200);
            expect(response.body).to.have.property("responseMap");
            expect(response.body).to.have.property("respTime");
            expect(response.body).to.have.property("message", "Success");
          });
        });
      });
    });
  });
});

