const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Verify PIN", () => {
  it("should verify the PIN successfully", () => {
    cy.fixture("responseHeader.json").then((responseHeader) => {
      cy.getEncodePin().then((encodePin) => {
        cy.request({
          method: "POST", 
          url: `${ENDPOINTS.appUrl}/security-sys/verifypin`,
          headers: {
            "wmt-mfs": responseHeader["wmt-mfs"],
            "deviceid": HEADERS.deviceid,
            "usertype": "customer",
            "content-type": "application/json"
          },
          body: {
            password: encodePin
          }
        }).then((response) => {
          cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);
          //Status code assertion
          expect(response.status).to.equal(200);

          //Response body assertion
          expect(response.body).to.have.property("message", "Verify Pin is success.");
          expect(response.body).to.have.property("statusCode");
          expect(response.body).to.have.property("respTime");
        });
      });
    });
  });
});
