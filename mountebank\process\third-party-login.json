{"predicates": [{"and": [{"matches": {"method": "GET", "path": "/security-sys/third-party-login", "headers": {"content-type": "application/json"}}}]}], "responses": [{"is": {"statusCode": 405, "headers": {"Content-Type": "application/json", "Vary": "Origin"}, "body": {"instance": "/security-sys/third-party-login", "detail": "problemDetail.org.springframework.web.HttpRequestMethodNotSupportedException", "title": "problemDetail.title.org.springframework.web.HttpRequestMethodNotSupportedException", "type": "about:blank", "status": 405}}}]}