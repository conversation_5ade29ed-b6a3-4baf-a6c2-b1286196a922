{"env": {"browser": true, "es6": true, "cypress/globals": true}, "globals": {"Atomics": "readonly", "SharedArrayBuffer": "readonly"}, "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "extends": ["eslint:recommended", "plugin:cypress/recommended"], "rules": {"semi": [2, "always"], "cypress/no-assigning-return-values": "error", "cypress/no-unnecessary-waiting": "error", "cypress/assertion-before-screenshot": "warn", "cypress/no-force": "warn", "cypress/no-async-tests": "error", "cypress/no-pause": "error", "indent": ["error", 2], "quotes": ["error", "double"]}, "plugins": ["cypress"]}