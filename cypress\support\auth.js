const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");
const pinPhone = Cypress.env("pinPhone");
const MERCHANT_ENDPOINTS = Cypress.env("merchant").endpoint;
const MERCHANT_HEADERS = Cypress.env("merchant").headers;
const WAVECHANNEL_HEADERS = Cypress.env("waveChannel").headers;

Cypress.Commands.add("loginWavePay", () => {
  let pin, password;
  cy.getEncodePin().then(resp => {
    pin = resp;
    cy.getEncodePin().then(resp => {
      password = resp;
      cy.request({
        method: "POST",
        url: `${ENDPOINTS.authUrl}/${ENDPOINTS.v2Url}/login`,
        failOnStatusCode: false,
        headers: HEADERS,
        form: true,
        body: {
          "msisdn": Cypress.env("msisdn"),
          "password": password,
          "pin": pin,
        }
      }).then(resp => {
        const body = {
          "msisdn": Cypress.env("msisdn"),
          "password": password,
          "pin": pin,
        };
        if (resp.status === 200) {
          resp.headers.wavepay = body;
          cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
        }
        else {
          cy.log("Login failed");
          cy.log(resp.body);
        }
      });
    });
  });
});

Cypress.Commands.add("getEncodePin", () => {
  cy.request({
    method: "GET",
    url: `${ENDPOINTS.authUrl}/wmt-mfs-otp/security-token`,
    
    headers: {
      "appId": HEADERS.appId,
      "appVersion": HEADERS.appVersion,
      "versionCode": HEADERS.versionCode,
      "deviceId": HEADERS.deviceId
    },
    
  }).then(resp => {
    expect(resp.status).to.equal(200);
    const securityToken = resp.body.responseMap.securityCounter;
    const content = `${pinPhone}:${securityToken}`;
    cy.task("getEncyptData", { content: content, environment: "test" }).then(encrypted => {
      return encrypted;
    });
  });
});

Cypress.Commands.add("getEncodePinMerchant", () => {
  let pinPhone = Cypress.env("merchant").pinPhone;
  cy.request({
    method: "GET",
    url: `${MERCHANT_ENDPOINTS.authUrl}/merchant-app/security-sys/get-security-token`,
    failOnStatusCode: false,
    headers: {
      "appId": MERCHANT_HEADERS.appId,
      "appVersion": MERCHANT_HEADERS.appVersion,
      "versionCode": MERCHANT_HEADERS.versionCode,
      "deviceId": MERCHANT_HEADERS.deviceId
    }
  }).then(resp => {
    expect(resp.status).to.equal(200);
    const securityToken = resp.body.securityCounter;
    const content = `${pinPhone}:${securityToken}`;
    cy.task("getEncyptData", { content: content, environment: "test" }).then(encrypted => {
      return encrypted;
    });
  });
});

Cypress.Commands.add("getEncodePinWaveChannel", () => {
  let pinPhone = Cypress.env("waveChannel").pinPhone;
  cy.request({
    method: "GET",
    url: `${ENDPOINTS.authUrl}/wmt-mfs-otp/security-token`,
    failOnStatusCode: false,
    headers: {
      "appId": WAVECHANNEL_HEADERS.appId,
      "appVersion": WAVECHANNEL_HEADERS.appVersion,
      "versionCode": WAVECHANNEL_HEADERS.versionCode,
      "deviceId": WAVECHANNEL_HEADERS.deviceId,
      "device": WAVECHANNEL_HEADERS.device,
      "product": WAVECHANNEL_HEADERS.product,
    },
    form: true,
  }).then(resp => {
    expect(resp.status).to.equal(200);
    const securityToken = resp.body.responseMap.securityCounter;
    const content = `${pinPhone}:${securityToken}`;
    cy.task("getEncyptData", { content: content, environment: "test" }).then(encrypted => {
      return encrypted;
    });
  });
});

Cypress.Commands.add("loginMerchant", () => {
  cy.getEncodePinMerchant().then(resp => {
    cy.request({
      method: "POST",
      url: `${MERCHANT_ENDPOINTS.authUrl}/merchant-app/security-sys/merchant-login`,
      failOnStatusCode: false,
      headers: MERCHANT_HEADERS,
      body: {
        "msisdn": Cypress.env("merchant").msisdn,
        "password": resp
      }
    }).then(resp => {
      if (resp.status === 200) {
        cy.writeFile("cypress/fixtures/merchantResponseHeader.json", resp.headers);
      }
      else {
        console.log(MERCHANT_HEADERS);
        cy.log("Login failed");
        cy.log(resp);
      }
    });
  });
});

Cypress.Commands.add("confirmOTP", (msisdn, otp) => {
  cy.request({
    method: "POST",
    url: `${MERCHANT_ENDPOINTS.authUrl}/merchant-app/security-sys/confirm-otp`,
    failOnStatusCode: false,
    headers: {
      "appId": MERCHANT_HEADERS.appId,
      "appVersion": MERCHANT_HEADERS.appVersion,
      "versionCode": MERCHANT_HEADERS.versionCode,
      "deviceId": MERCHANT_HEADERS.deviceId,
      "userLanguage": MERCHANT_HEADERS.userLanguage
    },
    form: true,
    body: {
      msisdn: msisdn,
      otp: otp
    }
  });
});

Cypress.Commands.add("getAccessTokenExternal", () => {
  cy.request({
    method: "POST",
    url: `${ENDPOINTS.authUrl}/auth/v1/token`,
    failOnStatusCode: false,
    form: true,
    body: {
      "client_id": Cypress.env("clientId"),
      "client_secret": Cypress.env("client_secret"),
      "grant_type": "client_credentials"
    }
  }).then(resp => {
    cy.log(resp.body);
  });
});


Cypress.Commands.add("getQRAccessTokenExternal", () => {
  cy.request({
    method: "POST",
    url: `${ENDPOINTS.authUrl}/auth/v1/qr/token`,
    failOnStatusCode: false,
    form: true,
    body: {
      "client_id": Cypress.env("clientId"),
      "client_secret": Cypress.env("client_secret"),
      "grant_type": "client_credentials"
    }
  });
});

Cypress.Commands.add("loginWaveChannel", () => {
  cy.getEncodePinWaveChannel().then(resp => {
    cy.request({
      method: "POST",
      url: `${ENDPOINTS.authUrl}/v2/mfs-agent/login`,
      failOnStatusCode: false,
      form: true,
      headers: {
        "device": WAVECHANNEL_HEADERS.device,
        "appid": WAVECHANNEL_HEADERS.appId,
        "appVersion": WAVECHANNEL_HEADERS.appVersion,
        "versionCode": WAVECHANNEL_HEADERS.versionCode,
        "deviceId": WAVECHANNEL_HEADERS.deviceId,
        "userLanguage": WAVECHANNEL_HEADERS.userLanguage
      },
      body: {
        "msisdn": Cypress.env("waveChannel").msisdn,
        "password": resp
      }
    }).then(resp => {
      expect(resp.status).to.equal(200);
      if (resp.status === 200) {
        cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
      }
      else {
        cy.log("Login failed");
        cy.log(resp);
      }
    });
  });
});

Cypress.Commands.add("getSecurityToken", () => {
  return cy.request({
    method: "GET",
    url: `${ENDPOINTS.authUrl}/wmt-mfs-otp/security-token`,
    headers: {
      "appId": HEADERS.appId,
      "appVersion": HEADERS.appVersion,
      "versionCode": HEADERS.versionCode,
      "deviceId": HEADERS.deviceId
    }
  }).then(resp => {
    expect(resp.status).to.equal(200);
    return resp.body.responseMap.securityCounter;
  });
});