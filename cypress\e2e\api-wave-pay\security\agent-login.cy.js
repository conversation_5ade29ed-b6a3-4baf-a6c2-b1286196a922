const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Agent Login", () => {
  it("should successfully login as an agent", () => {
    cy.fixture("responseHeader.json").then((responseHeader) => {
      cy.fixture("agent-login.json").then((loginData) => {
        cy.getEncodePin().then((encryptedPassword) => {
          loginData.agentSuccessLogin200 = { ...loginData.agentSuccessLogin200, password: encryptedPassword };
          
          cy.request({
            method: "POST",
            url: `${ENDPOINTS.appUrl}/security-sys/agent-login`,
            headers: {
              "wmt-mfs": responseHeader["wmt-mfs"],
              "usertype": "agent",
              "deviceid": HEADERS.deviceId,
              "content-type": "application/json",
            },
            body: loginData.agentSuccessLogin200
          }).then((response) => {
            cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);
            // Status code assertion
            expect(response.status).to.equal(200);

            // Response body assertions
            expect(response.body).to.have.property("responseMap");
            expect(response.body.responseMap).to.have.property("msisdn");
            expect(response.body.responseMap).to.have.property("userType");
            expect(response.body.responseMap).to.have.property("agentId");
            expect(response.body.responseMap).to.have.property("userCategory");
            expect(response.body.responseMap).to.have.property("isWhitelistedAgent");
            expect(response.body).to.have.property("message");

            // Version info assertions
            expect(response.body.responseMap.versionInfo).to.have.property("forcedUpgrade");
            expect(response.body.responseMap.versionInfo).to.have.property("versionName");
            expect(response.body.responseMap.versionInfo).to.have.property("versionCode");
          });
        });
      });
    });
  });
});