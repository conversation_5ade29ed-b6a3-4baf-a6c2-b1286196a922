const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");
const MSISDN = Cypress.env("msisdn");

describe('Save Device ID', () => {
  it("To test save-device-id return 200 success", () => {
    cy.fixture("responseHeader.json").then((respHeader) => {
      cy.request({
        method: "POST",
        url: `${ENDPOINTS.appUrl}/security-sys/save-device-id`,
        headers: {
          "wmt-mfs": respHeader["wmt-mfs"],
          "appId": HEADERS.appId,
          "appVersion": HEADERS.appVersion,
          "versionCode": HEADERS.versionCode,
          "deviceId": HEADERS.deviceId,
        },
        body: "msisdn:" + MSISDN
      }).then(resp => {
        cy.writeFile("cypress/fixtures/responseHeader.json", resp.headers);
        expect(resp.status).to.equal(200);
        expect(resp.body).to.have.property("responseMap");
        expect(resp.body).to.have.property("message");
        expect(resp.body.message).to.equal("The device id was added successfully");
      });
    });
  });
});

