const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Forgot PIN API testing", () => {
    it("To test success request", () => {
        cy.request({
            method: "POST",
            url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin`,
            headers: {
                "content-type": "application/json",
                "appid": HEADERS.appId,
                "appVersion": HEADERS.appVersion,
                "versionCode": HEADERS.versionCode,
                "deviceid": HEADERS.deviceId
            },
            body: {
                msisdn: Cypress.env("msisdn")
            }
        }).then((response) => {
            expect(response.status).to.eq(200);
            expect(response.body).to.have.property("responseMap");
            expect(response.body.responseMap).to.have.property("code", "PIN_IS_RESET");
            expect(response.body).to.have.property("message", "Your PIN has been reset");
            expect(response.body).to.have.property("respTime");
        });
    });

    it("To test verify NRC request", () => {
        cy.fixture("forgotPin.json").then((payload) => {
            cy.request({
                method: "POST",
                url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin`,
                headers:{
                    "content-type": "application/json",
                    "appid": HEADERS.appId,
                    "appVersion": HEADERS.appVersion,
                    "versionCode": HEADERS.versionCode,
                    "deviceid": HEADERS.deviceId
                },
                body: payload.verify_NRC
            }).then((response) => {
                expect(response.status).to.eq(200);
                expect(response.body).to.have.property("responseMap");
                expect(response.body.responseMap).to.have.property("code", "VERIFY_NRC");
                expect(response.body).to.have.property("message", "Please enter your NRC");
                expect(response.body).to.have.property("respTime");
            })
        })
    })

    it("To test verify DOB request", () => {
        cy.fixture("forgotPin.json").then((payload) => {
            cy.request({
                method: "POST",
                url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin`,
                headers:{
                    "content-type": "application/json",
                    "appid": HEADERS.appId,
                    "appVersion": HEADERS.appVersion,
                    "versionCode": HEADERS.versionCode,
                    "deviceid": HEADERS.deviceId
                },
                body: payload.verify_DOB
            }).then((response) => { 
                expect(response.status).to.eq(200);
                expect(response.body).to.have.property("responseMap");
                expect(response.body.responseMap).to.have.property("code", "VERIFY_DOB");
                expect(response.body).to.have.property("message", "Please enter your date of birth");
                expect(response.body).to.have.property("respTime");
            })
        })
    })
});