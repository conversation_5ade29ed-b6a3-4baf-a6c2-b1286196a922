# API testing with Cypress

## Installation
Requirements
- Node.js 12 or 14 and above
- Clone API project automation
- Open cmd/terminal and change the directory to project path

Running Cypress
```bash
npm install 
```

## Usage

```
# Setup env and test data
1. Change file name cypress.env.json.example to cypress.env.json
2. Add data in cypress.env.json
3. Change test data in /fixtures/

# Open cypress
npm run cy:open

# Run cypress headless
npm run cy:run

# Get pin for login and put it in cypress.env.json
1. Run crypto library (with any IDE : Intellij or Eclipse)
2. Type `npm run cy:getPin` in cmd/terminal (the path should be in the project directory)
3. encoded pin and password will be in cypress/fixtures/login.json

```

### [August 2024] Update to support multiple environment 

available environmentName  [ppd|sit|local]

|---------------------------------------------------------------------------|
| npm run cy:[yourScript_in-package.json] -- --env environmentName=ppd      |
|---------------------------------------------------------------------------|

this command add environment variable on script to call difference cypress.env file to run on select environment. Default value is 'local' we create this to support CI process by submit environmentname.

You have to modify value in each cypress.env file to match your test data on each environment

cypress.env.local.json      >>>>    mountebank environment
cypress.env.sit.json        >>>>    sit environment
cypress.env.preprod.json    >>>>    pre production environment

## Run API Test

SmartyPants converts ASCII punctuation characters into "smart" typographic punctuation HTML entities. For example:

|                   |Commands                        |
|-------------------|--------------------------------|
|Cypress GUI        |`npm run cy:open`               |
|Specifile          |`npm run cy:runCustomerMA `     |
|All files          |`npm run cy:runMoneyTransfer`   |


## Project structure
```
.
├── e2e                  # Contain all test files
├── fixtures             # Store the test data which can be read by multiple tests
├── support              # Include code before your test files (Use for login)
.
── README.md
── Cypress.env.json               # contain generic environment variables 
── cypress.env.local.json         # contain local environment variables for execute cypress on loccal machine
── Cypress.env.mountebank.json    # contain mountebankd endpoint and variables for execute on Jenkin
── Cypress.env.ppd.json           # contain environment endpoint and variables for execute on preprod env
── Cypress.env.sit.json           # contain environment endpoint and variables for execute on sit env
```

## Add a new script
1. Create a test file with [your_test_name].cy.js
2. Add test code -> describe("Test suite, Can be url path") 
    and it("Test case name") e.g. it("To verify user can login")
3. Add  `cy.fixture("responseHeader.json").then((respHeader) => {}` to get wmt-mfs token
4. Send a request with cy.request()

Exampe
```
const ENDPOINTS = Cypress.env("endpoint");  // To get baseURL
const HEADERS = Cypress.env("headers");     // To get required headers

describe("v2/mfs-customer/balance", () => {  // Test suite name
  it("To verify user can get the remaining balance", () => { // Test case name
    cy.fixture("responseHeader.json").then((respHeader) => { // Get wmt-mfs token
      cy.request({    // Send request with GET method
        method: "GET",
        url: `${ENDPOINTS.baseUrl}/${ENDPOINTS.v2Url}/wallet-balance`,  // Request to URL wallet-balance
        failOnStatusCode: false,
        headers: {  // Send request with headers
          "wmt-mfs": respHeader["wmt-mfs"],  // Get from file responseHeader.json
          "appId": HEADERS.appId,            // Get from cypress.env.json file
          "appVersion": HEADERS.appVersion,
          "versionCode": HEADERS.versionCode,
          "deviceId": HEADERS.deviceId
        }
      }).then(resp => {
        expect(resp.status).to.equal(200);  // Assertion
        expect(resp.body.responseMap).to.have.property("balance");
        // You can do chain request here 
        // Example check balance then send money and verify the balance
      });
    });
  });
});

```

## Add a custom command
1. Add command in e2e.js file
2. Add reusable api endpoint create custom command name "getWalletBalance"(to use wallet-balance api)
3. cy.getWalletBalance() get return balance.
```
Cypress.Commands.add("getWalletBalance", () => {
  let balance;
  cy.readFile("cypress/fixtures/responseHeader.json").then((respHeader) => {// read json data fom respHeader's json file
    cy.request({
      method: "GET",
      url: `${ENDPOINTS.baseUrl}/${ENDPOINTS.v2Url}/wallet-balance`,
      failOnStatusCode: false,
      headers: {// Send request with headers
        "wmt-mfs": respHeader["wmt-mfs"],  // Get from file responseHeader.json
        "appId": HEADERS.appId,            // Get from cypress.env.json file
        "appVersion": HEADERS.appVersion,
        "versionCode": HEADERS.versionCode,
        "deviceId": HEADERS.deviceId
      },
      body: {
        "msisdn": msisdn
      }
    }).then(resp => {
      expect(resp.status).to.equal(200);// Assertion
      expect(resp.body.responseMap).to.have.property("balance");// Assertion
      expect(resp.body.responseMap.balance).to.be.a("number");// Assertion
      balance = resp.body.responseMap.balance;
      return balance;
    });
  });

it("To verify user do bill payment with Alliance", () => {
    cy.readFile("cypress/fixtures/responseHeader.json").then((respHeader) => {
      cy.getWalletBalance().then((resp) => {// call getWalletBalance command to get balance
        let initBalance = resp;
        cy.fixture("offlineBillers.json").then((biller) => {
          // Add pin into the request
          biller.loanAlliance = { ...biller.loanAlliance, pin: respHeader.wavepay.pin };
          const formData = JSON.stringify(biller.loanAlliance.inputFields);
          biller.loanAlliance.inputFields = formData;
          let amount = biller.loanAlliance.totalBillAmount;
          cy.request({
            method: "POST",
            url: `${ENDPOINTS.baseUrl}/${ENDPOINTS.v2Url}/bill-pay`,
            failOnStatusCode: false,
            headers: {
              "wmt-mfs": respHeader["wmt-mfs"],
              "appId": HEADERS.appId,
              "appVersion": HEADERS.appVersion,
              "versionCode": HEADERS.versionCode,
              "deviceId": HEADERS.deviceId,
            },
            form: true,
            body: biller.loanAlliance
          }).then(resp => {
            expect(resp.status).to.equal(200);
            expect(resp.body).to.have.property("responseMap");
            cy.getWalletBalance().then((resp) => {// call getWalletBalance command to get balance
              let netBalance = resp;
              expect(initBalance - amount).to.equal(netBalance);// Assertion 
            });
          });
        });
      });
    });
  });

```