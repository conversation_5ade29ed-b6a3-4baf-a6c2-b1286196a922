

function (config) {
    const logger = config.logger;
    const querystring = require('querystring');
    let body = {};

    logger.info('Raw request body:', config.request.body);

    try {
        // Parse URL-encoded body
        body = querystring.parse(config.request.body);
    } catch (e) {
        logger.error('Error parsing URL-encoded data:', e);
        return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Invalid data format' })
        };
    }

    logger.info('Parsed body:', body);

    if (body.senderno && body.receiverno && body.senderno === body.receiverno) {
        return {
            statusCode: 400,
            body: JSON.stringify({ message: 'Sender and Receiver number cannot be the same' })
        };
    }

    return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Transaction processed successfully' })
    };
}