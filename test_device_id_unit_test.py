#!/usr/bin/env python3
"""
Device ID Consistency Unit Test

This script tests the device ID consistency logic without requiring external services.
It validates the implementation of device ID storage and validation methods.

Usage:
    python test_device_id_unit_test.py

Author: WaveMoney Team
"""

import sys
import os

# Add the current directory to the path so we can import test_flow_apis
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_flow_apis import WavePayPinEncoder

def test_device_id_storage_and_validation():
    """
    Test device ID storage and validation logic
    """
    print("=" * 80)
    print("DEVICE ID CONSISTENCY UNIT TEST")
    print("=" * 80)
    
    # Test configuration
    environment = "sit"
    
    print(f"Test Configuration:")
    print(f"  Environment: {environment}")
    
    # Create encoder with debug mode enabled
    encoder = WavePayPinEncoder(environment=environment, debug=True)
    
    print(f"\nInitial State:")
    print(f"  Environment Device ID: {encoder.headers.get('deviceId')}")
    print(f"  Login Device ID (stored): {encoder.login_device_id}")
    print(f"  Login Token (stored): {encoder.login_token}")
    print(f"  Login MSISDN (stored): {encoder.login_msisdn}")
    
    # Test 1: Initial state validation
    print(f"\n{'='*60}")
    print("TEST 1: Initial State Validation")
    print(f"{'='*60}")
    
    assert encoder.login_device_id is None, "Login device ID should be None initially"
    assert encoder.login_token is None, "Login token should be None initially"
    assert encoder.login_msisdn is None, "Login MSISDN should be None initially"
    print("✓ PASS: Initial state is correct")
    
    # Test 2: Manual session data setup (simulating successful login)
    print(f"\n{'='*60}")
    print("TEST 2: Session Data Storage")
    print(f"{'='*60}")
    
    test_device_id = "233b12401ce89f35dbf56af9925a3b0a714bb74f"
    test_token = "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2In0.example-token"
    test_msisdn = "9799307549"
    
    # Simulate login success by manually setting session data
    encoder.login_device_id = test_device_id
    encoder.login_token = test_token
    encoder.login_msisdn = test_msisdn
    
    print(f"Simulated login session data:")
    print(f"  Device ID: {encoder.login_device_id}")
    print(f"  Token: {encoder.login_token[:15]}...")
    print(f"  MSISDN: {encoder.login_msisdn}")
    
    assert encoder.login_device_id == test_device_id, "Device ID should be stored correctly"
    assert encoder.login_token == test_token, "Token should be stored correctly"
    assert encoder.login_msisdn == test_msisdn, "MSISDN should be stored correctly"
    print("✓ PASS: Session data storage works correctly")
    
    # Test 3: Device ID consistency validation
    print(f"\n{'='*60}")
    print("TEST 3: Device ID Consistency Validation")
    print(f"{'='*60}")
    
    # Test with matching device ID
    is_consistent = encoder.validate_device_id_consistency(test_device_id)
    assert is_consistent == True, "Device ID consistency should return True for matching IDs"
    print("✓ PASS: Matching device ID validation works")
    
    # Test with non-matching device ID
    different_device_id = "939926174f5ef028941de81b4a1f44453b166f23"
    is_consistent = encoder.validate_device_id_consistency(different_device_id)
    assert is_consistent == False, "Device ID consistency should return False for different IDs"
    print("✓ PASS: Non-matching device ID validation works")
    
    # Test 4: Consistent OTP headers generation
    print(f"\n{'='*60}")
    print("TEST 4: Consistent OTP Headers Generation")
    print(f"{'='*60}")
    
    try:
        otp_headers = encoder.get_consistent_otp_headers()
        
        # Validate that the headers contain the correct device ID
        assert otp_headers.get('deviceId') == test_device_id, "OTP headers should use login device ID"
        assert otp_headers.get('wmt-mfs') == test_token, "OTP headers should use login token"
        assert 'appId' in otp_headers, "OTP headers should include appId"
        assert 'appVersion' in otp_headers, "OTP headers should include appVersion"
        
        print(f"Generated OTP headers:")
        print(f"  Device ID: {otp_headers.get('deviceId')}")
        print(f"  Token: {otp_headers.get('wmt-mfs')[:15]}...")
        print(f"  App ID: {otp_headers.get('appId')}")
        print(f"  Headers count: {len(otp_headers)}")
        
        print("✓ PASS: Consistent OTP headers generation works")
        
    except Exception as e:
        print(f"✗ FAIL: OTP headers generation failed: {e}")
        return False
    
    # Test 5: Error handling for missing session data
    print(f"\n{'='*60}")
    print("TEST 5: Error Handling for Missing Session Data")
    print(f"{'='*60}")
    
    # Create a new encoder without session data
    encoder_no_session = WavePayPinEncoder(environment=environment, debug=False)
    
    # Test validation with no login device ID
    is_consistent = encoder_no_session.validate_device_id_consistency(test_device_id)
    assert is_consistent == False, "Validation should return False when no login device ID is stored"
    print("✓ PASS: Validation correctly handles missing login device ID")
    
    # Test OTP headers generation with no session data
    try:
        encoder_no_session.get_consistent_otp_headers()
        print("✗ FAIL: Should have raised ValueError for missing session data")
        return False
    except ValueError as e:
        print(f"✓ PASS: Correctly raised ValueError for missing session data: {e}")
    
    return True

def main():
    """
    Main function to run the device ID consistency unit tests
    """
    print("Starting Device ID Consistency Unit Tests...")
    
    success = test_device_id_storage_and_validation()
    
    print(f"\n{'='*80}")
    if success:
        print("OVERALL RESULT: PASS")
        print("All device ID consistency unit tests passed successfully.")
        print("The implementation correctly handles:")
        print("  ✓ Session data storage")
        print("  ✓ Device ID consistency validation")
        print("  ✓ Consistent OTP headers generation")
        print("  ✓ Error handling for missing data")
    else:
        print("OVERALL RESULT: FAIL")
        print("Some unit tests failed. Please review the implementation.")
    print("="*80)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
