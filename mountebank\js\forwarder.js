function (config) {

    if (typeof config.state.requests === 'undefined') {
        config.state.requests = {};
    }
    
    var http = require('http'),
        options = {
            method: config.request.method,
            hostname: 'localhost',
            port: 4043,
            path: '/service/otp/9799307459',
            headers: config.request.headers
        },
        httpRequest = http.request(options, response => {
            var body = '';
            response.setEncoding('utf8');
            response.on('data', chunk => {
                body += chunk;
            });
            response.on('end', () => {
                var stubResponse = {
                        statusCode: response.statusCode,
                        headers: response.headers,
                        body
                    };
                config.logger.info('Successfully proxied: ' + JSON.stringify(stubResponse));
                config.callback(stubResponse);
            });
        });
    httpRequest.end();
}