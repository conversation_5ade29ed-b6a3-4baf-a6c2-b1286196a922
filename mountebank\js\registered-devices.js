function(){
    // get real time with format Feb 27, 2025 02:45:28 PM
    var date = new Date();
    var options = { year: 'numeric', month: 'long', day: 'numeric', 
        hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: true };
    var formattedDate = date.toLocaleString('en-US', options);
    return {
        "statusCode": 200,
        headers: {
            "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2Iiwia2lkIjoibWZzIiwiY3R5IjoiSldUIn0.nmA03poWKlA_v8i8qq2G4pYAGcsI2GHNyQgscNmOs_RlENyJneXxcAn-ARqT8COhOJNdt-QVK3WQsvz2eXHkmvZ9-mwT8MmevNhO1-6qsPoWkkcuLQ4H7aUhMOrCOniCLiO6VPoeCjyQJH2uqOf8KWhEBOfoDkF9B6K4S5GDhzWNAU3yI0X2Krfb8KqUetSnmhm-y5Jgg_0Aqp5CjpO58E95iI6tjvvw3K3SbDg0_Vu2yHT4msa018Hu5v6Kp_6FZ1RSVhwPIPlo-oh7qQcMHD3wcv06t8jmd1euxFHAQCe04-R3RjF7ZFUKDfWAXN50owKQk1DtHjMbji4_r9j4vQ.Zigd2a3Q5WichP0YD5ahBg.4jHtkDPUQPJa3oQIMGFhrVacQsqC1ygyD3-NBLHQha-S78NV1TyDCm2DBpuhS6SMvcvG82jC7BPTObG0sog_aENtZFHaJ8SN9l38oq2CMcAfV1h_SYkyIvbNqyfv_Ockp7ikgakax0waFEsZTPKqUJvNO0uFJXUOJ0b-7MwKoGsVFtE7JYprQJJqXdwJgA6D1hsTunt3claUvJnB5WIGMQb7rntAWHkL1V_aQDHju_gRoGV2M_s7USNqM3QhRifznwXm9I_nTaMIW2xe31UE4OCtOwRmL0LxQIHDDDPgaEdfSdykPKIV1d5bsdmyIPHTBBaGqsQjEdrKc09ItfL7_Znjgs9n8wU2kNxdtohWjne231vbLtK99G_N1_CNphjIypzelyaOBDrj0RXi9p0Z137ChH0L8ILQme1TZqxmO5DNMvx3AXoYFAS4PegvHsi5xDygWrkDqajNlQWvlfMuQ_Mylj-z17I9s883bvGL1JMtU_AIWKiG3RNeA9IYMJAUmHifIL25WNN9Wy700WyB_iNLcTQRCTDpooQuNv8BLJnNaefqCHlRiA-b-RSHbecWQ6YgeO80Won8dEpnVIoN0SA_tCvf6yfIM9SJWQmSUuoX0lfhS25JqIAr9kd3INJHfOBU0WtWgDe-RFsqk62U-vc5DHr0XRnjkxPcoBcMookEDmng5sMX9enuwOadD2mw6GPjwi7WNxORBK72SpzHGzMeIVjWbihDPEnP6a4GzG4mVruGqBgVZsV2-ZN7bnt1xtbFtk3aAGx3rzPEH74M3dyynduxcuzHAHvdbJulmiajlvqhP_RozH5T1P0kDpViIqM5-VtOK-2WIevF7OzPr_9MaNvoASKZRg5AW34thxB5sdsTk6PF7TK_OPm4VHm7_8DTdFp6oPomUEGTMDfRwHNYa9WmnAzIfkg720I5PE9HU8KAk4fDSihx4yWBDTG9Q7ZjIvdkvWMxy96e_ni_35I5gJuMTVEqoSIiQ9YgKgiL0kAr0SwGNhucZcs-3ThJu-2D6w8Ln7Dj2DO1Gci2UisiDhKIe3a8AhvoVrKg4A7KX68ODj1xm-4FNBgnIpjCugCKojNCNsy6VGWT2BG7NV5WGQEzpQLaFnTBgtwhgyrNJ1INTPGo4ZkRNC70cM4J-V7bKLvv6YFY4M6NOfTaSqtbTtuqphrhXTCl3a_LvS8_Iz0CSzj7vPT5FQ6qrfZrioc5nmPCnXtED9La7Hzuf3pN48-Ckm9InkSzptMBsrOR_-uJhsvGV09EDqAvlnRHJM1iLb8bCtRh65T9e2WHO-XfanuNiSfHVKrmOTGtT_SfA1HcjHIF9MIl907KoItWF41YTlEOx2LuaKYv99N652UtwaFE9f6m8gdQ5Ax9q2JRjo9_P7-4_KLFbv2LzwbV9mu76qyiCGwLEliWD6lR8b7k61CIJ6vU7apzuLU5g0mHeo6niI1wYBur6MO26NKde7jX8jTKNn5TU_nbpyJMDAoF8uzy9fpV9bIMwvNUa4lcbWYkKols-X7gL-Ygy3yQ814mKo_ITaZU3rIxnTQUx55VwLmFD7Ofi_fYt5P5QRk.1qAdfhGu1y15FasE1CpO0Q",
            "Content-Type": "application/json"
        },
        body: JSON.stringify({'responseMap': {}, 'respTime': formattedDate, 'message': 'Success'})
    }
}