const { defineConfig } = require("cypress");
const CryptoService = require("./cypress/encrypts/CryptoService");
const path = require("path");
const fs = require("fs");

module.exports = defineConfig({
  e2e: {
    setupNodeEvents(on, config) {
      // Load environment-specific settings
      const environmentName = config.env.environmentName || "local";
      const environmentFilename = `./cypress.env.${environmentName}.json`;

      try {
        const settings = require(environmentFilename);
        config.env = {
          ...settings,
        };
        console.log("Loaded settings for environment:", environmentName);
        console.log("Endpoint:", config.env.endpoint);
      } catch (error) {
        console.error("Error loading settings file:", error.message);
        throw error;
      }

      // Retain existing tasks with config passed to each task
      on("task", {
        log(message) {
          console.log(message);
          return null;
        },
        getEncyptData({ content, environment }) {
          const cryptoService = new CryptoService();
          return cryptoService.encrypt(content, config.env.environmentName || environment);
        },
        setValue: (params) => {
          const { key, value } = params;
          data[key] = value;
          return value;
        },
        getValue: (params) => {
          const { key } = params;
          return data[key] || null;
        },
      });

      // Rename mochawesome JSON report and JUnit XML report dynamically
      on("after:spec", (spec, results) => {
        const jsonReportDir = path.join("reports", "json");
        const xmlReportDir = path.join("reports", "xml");

        // Ensure directories exist
        if (!fs.existsSync(jsonReportDir)) {
          fs.mkdirSync(jsonReportDir, { recursive: true });
        }
        if (!fs.existsSync(xmlReportDir)) {
          fs.mkdirSync(xmlReportDir, { recursive: true });
        }

        // Handle JSON report
        const defaultJsonPath = path.join("reports", "mochawesome.json");
        const specNameWithoutExt = path.basename(spec.relative, ".cy.js"); // Remove .cy.js
        const customJsonPath = path.join(jsonReportDir, `report-${specNameWithoutExt}.json`);

        if (fs.existsSync(defaultJsonPath)) {
          try {
            fs.renameSync(defaultJsonPath, customJsonPath);
            console.log(`JSON report renamed to: ${customJsonPath}`);
          } catch (err) {
            console.error("Error renaming JSON report file:", err.message);
          }
        }

        // Handle XML report
        const defaultXmlPath = path.join("reports", "results.xml");
        const customXmlPath = path.join(xmlReportDir, `report-${specNameWithoutExt}.xml`);

        if (fs.existsSync(defaultXmlPath)) {
          try {
            fs.renameSync(defaultXmlPath, customXmlPath);
            console.log(`XML report renamed to: ${customXmlPath}`);
          } catch (err) {
            console.error("Error renaming XML report file:", err.message);
          }
        }
      });

      // Return the updated config object for Cypress to use it
      return config;
    },
  },
  reporter: "cypress-multi-reporters",
  reporterOptions: {
    reporterEnabled: "mochawesome, mocha-junit-reporter",
    mochawesomeReporterOptions: {
      charts: false,
      json: true,
      reportDir: "reports", // Default directory for mochawesome
      reportFilename: "mochawesome", // Default report name before renaming
      overwrite: false,
    },
    mochaJunitReporterReporterOptions: {
      mochaFile: "reports/results.xml", // Default XML file
      toConsole: false,
    },
  },
  chromeWebSecurity: false,
  video: false,
});
