var request = JSON.parse(process.env.MB_REQUEST),
    response = JSON.parse(process.env.MB_RESPONSE),
    requestId = request.path.match(/\/service\/otp\/transformer\/(\d+)/),
    captureID = requestId ? requestId[1] : null,
    fs = require('fs'),
    mappings = fs.readFileSync('js/user.csv', { encoding: 'utf8' }),
    lines = mappings.split(/\r?\n/);

for (let i = 0; i < lines.length; i += 1) {
    var fields = lines[i].split('|'),
        id = fields[0],
        name = fields[1];

    if (captureID === id) {
        response.body = response.body.replace('${YOU}', name);
    }
}

console.log(JSON.stringify(response));


const fs = require('fs');

const filePath = 'js/user.csv';

fs.appendFile(filePath, newEntry, (err) => {
    if (err) {
        console.error('Error writing to CSV file:', err);
    } else {
        console.log('Data successfully added to CSV file');
    }
});

var request = JSON.parse(process.env.MB_REQUEST),
    response = JSON.parse(process.env.MB_RESPONSE),
    requestId = request.form.match("msisdn"),
    captureID = requestId ? requestId[1] : null,
    fs = require('fs'),
    mappings = fs.readFileSync(filePath, { encoding: 'utf8' }),
    lines = mappings.split(/\r?\n/);

for (let i = 0; i < lines.length; i += 1) {
    var fields = lines[i].split('|'),
        id = fields[0],
        name = fields[1];

    if (captureID === id) {
        response.body = response.body.replace('${YOU}', name);
    }
}

// New entry to check

fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading CSV file:', err);
        return;
    }

    // Split file content into lines
    const lines = data.split(/\r?\n/);

    // Check for duplicate
    if (lines.includes(newEntry)) {
        console.error('Duplicate entry found: Operation aborted');
    } else {
        // Append the new entry
        fs.appendFile(filePath, `${newEntry}\n`, (err) => {
            if (err) {
                console.error('Error writing to CSV file:', err);
            } else {
                console.log('Data successfully added to CSV file');
            }
        });
    }
});
