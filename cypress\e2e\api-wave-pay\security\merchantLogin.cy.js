const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Merchant Login", () => {
  it("should successfully login as a merchant", () => {
    cy.fixture("responseHeader.json").then((responseHeader) => {
      cy.fixture("merchantLogin.json").then((loginData) => {
        cy.getEncodePin().then((encryptedPassword) => {
          loginData.merchantLogin200 = { ...loginData.merchantLogin200, password: encryptedPassword };
          cy.request({
            method: "POST",
            url: `${ENDPOINTS.appUrl}/security-sys/merchant-login`,
            headers: {
              "wmt-mfs": responseHeader["wmt-mfs"],
              "appId": HEADERS.appId,
              "appVersion": HEADERS.appVersion,
              "versionCode": HEADERS.versionCode,
              "deviceId": HEADERS.deviceId,
              "content-type": "application/json"
            },
            body: loginData.merchantLogin200
          }).then((response) => {
            cy.writeFile("cypress/fixtures/responseHeader.json", response.headers);

            expect(response.status).to.equal(200);            
            // Response body assertion
            expect(response.body).to.have.property("agentDetails");
            expect(response.body.agentDetails).to.have.property("agentId");
            expect(response.body.agentDetails).to.have.property("agentCategory");
            expect(response.body.agentDetails).to.have.property("agentName");
            expect(response.body.agentDetails).to.have.property("agentGroup");
            
            expect(response.body).to.have.property("subscriberDetails");
            expect(response.body.subscriberDetails).to.have.property("kycStatus");
            expect(response.body.subscriberDetails).to.have.property("gender");
            expect(response.body.subscriberDetails).to.have.property("dob");
            expect(response.body.subscriberDetails).to.have.property("name");
            expect(response.body.subscriberDetails).to.have.property("msisdn");
            expect(response.body.subscriberDetails).to.have.property("nrc");
          });
        });
      });
    });
  });
}); 