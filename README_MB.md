# API testing with Cypress

## Installation

```bash
npm install -g mountebank

# After installing mountebank, to install other dependencies, run

npm install
```

Requirements

- Node.js 12 or 14 and above
- Clone API project mockserver
- Open cmd/terminal and change the directory to project path


## Run API mockserver

```bash
cd [your project path]

#command for automatic restart mountebank server when any file in project is update
npm run watch

#command to start mountebank server 
## ***need to rerun everytime you change the code***
mb restart --configfile mountebank/imposters.ejs --allowInjection --logfile mountebank/log/mb.log
```

## Project structure

```bash

├── js                  # Contain javascript file for injection in mocking file
├── log                 # Contain log information while run mockserver
├── process             # Folder that contains all mocking service files
├── support             # Contain common function file that support MB service and ref file 
├── imposters.ejs       # Main file that use for run and where you need to add your mocking file 

├── node_modules        # Contain common lib, mobule and dependencies that project used 
├── reports             # Contain report result generate from scritp execution 

── README.md
```

## Add a new script

1. Create a moutebank json file as "[your_endpoint_name].json" under process folder
2. Add your mountebank json in 'imposters.ejs' file under 'stub' section
3. Add predicates and responses in your mountebank json file
    a. Suggest to use regex in predicate need to use "matches": operator
4. Open terminal, go to you working folder and run mountebank server with 'npm run watch' command
5. Validate your mocking service with postman according your test case

Example of predicates

```json
{
  "predicates": [{
      "equals":{
        "method": "POST",
        "path": "/v2/mfs-customer/login"
      }
  }],
  "responses": [
    {
      "is": {
      "statusCode": 200,
      "headers": {
        "Content-Type": "application/json"
      },
      "body": {
        "responseMap": {
            "userType": 2,
            "userCategory": "pos",
            "settings": {
                "fields_values": {
                    "mfs.language": [
                        "English",
                        "Burmese"
                    ],
                    "mfs.charge_type": [
                        "MFS",
                        "CC"
                    ]
                }
            },
            "versionInfo": {
                "versionCode": 3013,
                "versionName": "3.1.0",
                "directUrl": "https://www.wavemoney.com.mm/wavepartner.apk",
                "forcedUpgrade": "true"
            },
            "msisdn": "9797760204",
            "agentId": 9152144
        },
        "message": "Success"
      }
    }
  }]
}
```
