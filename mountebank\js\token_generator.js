function (config) {
    const logger = config.logger;

    logger.info('Processing request for dynamic response');

    // Generate dynamic data
    function generateUUID() {
        // Fallback UUID generator
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
    
    const token = Buffer.from(JSON.stringify({
        alg: "RS" + Math.floor(Math.random() * 256), // Random RS algorithm number
        enc: ["A128CBC-HS256", "A256GCM"][Math.floor(Math.random() * 2)], // Random enc
        kid: generateUUID(), // Random UUID
        cty: "JWT"
    })).toString('base64');

    const responseTime = new Date().toUTCString();

    return {
        headers: {
            "Content-Type": "application/json",
            "wmt-mfs": token
        },
        body: JSON.stringify({
            responseMap: {
                accountStatus: 2,
                agentId: Math.floor(Math.random() * *********),
                googleUrl: "https://wave.mn/apa",
                subscriberDetails: {
                    kycStatus: 2,
                    requireKYCUpgrade: false,
                    name: "Tester"
                },
                directUrl: "https://wave.mn/apd",
                interstitialData: {
                    offerType: 1,
                    imageUri: "https://files.wavemoney.io:8199/splash-screens/splash_screen_apr_2019.jpg",
                    headerName: "Announcement!",
                    messageId: "Message145",
                    offerActionName: "",
                    type: 1,
                    title: "This version will be stopped soon. Please upgrade to new WavePay App to enjoy more features!",
                    messageExpiry: "20200930T13:12:28+0530"
                },
                userType: 0,
                versionInfo: {
                    iOS: {
                        appStoreUrl: "https://itunes.apple.com/us/app/wavepay/id1439175549",
                        forcedUpgrade: "true",
                        build: 43,
                        version: "2.1.0"
                    },
                    Android: {
                        forcedUpgrade: "false",
                        playStoreUrl: "https://play.google.com/store/apps/details?id=mm.com.wavemoney.wavepay",
                        directUrl: "https://app.adjust.com/pbka9us?campaign=Media+Link&adgroup=Media+link_WP&redirect=https%3A%2F%2Fwavemoney.com.mm%2Fwavepay-v2-2-0.apk",
                        versionName: "2.2",
                        versionCode: 1450
                    }
                },
                versionName: "WavePay",
                config: {
                    kycRequiredGroups: "10042"
                },
                versionCode: 1411
            },
            respTime: responseTime,
            message: "Success",
            statusCode: 0
        })
    };
}
