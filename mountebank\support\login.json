{
  "predicates": [{
    "matches": {
        "method": "POST",
        "path": "/v3/mfs-customer/login"
    }
  }],
  "responses": [
    {
      "is": {
      "statusCode": 200,
      "headers": {
        "Content-Type": "application/json"
      },
      "body": {
        "responseMap": {
            "userType": 2,
            "userCategory": "pos",
            "settings": {
                "fields_values": {
                    "mfs.language": [
                        "English",
                        "Burmese"
                    ],
                    "mfs.charge_type": [
                        "MFS",
                        "CC"
                    ]
                }
            },
            "versionInfo": {
                "versionCode": 3013,
                "versionName": "3.1.0",
                "directUrl": "https://www.wavemoney.com.mm/wavepartner.apk",
                "forcedUpgrade": "true"
            },
            "msisdn": "9797760204",
            "agentId": 9152144
        },
        "message": "Success"
      }
    }
  }]
},
{
  "predicates": [{
    "matches": {
        "method": "POST",
        "path": "/v2/mfs-customer/login"
    }
  }],
  "responses": [
    {
      "inject": "<%- stringify(filename, 'js/token_generator.js') %>"
  }]
}