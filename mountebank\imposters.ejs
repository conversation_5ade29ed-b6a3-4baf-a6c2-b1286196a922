{
    "imposters": [
            {
                    "port": "4045",
                    "protocol": "http",
                    "name": "test",
                    "stubs": [
                    <% include support/security-token.json %>,
                    <% include support/login.json %>,
                    <% include process/forgot-pin.json %>,
                    <% include process/wavepay-security-token.json %>,
                    <% include process/save-device-id.json %>,
                    <% include process/nonces-security-token.json %>,
                    <% include process/merchant-login.json %>,
                    <% include process/registered-devices.json %>,
                    <% include process/agent-login.json %>,       
                    <% include process/verifypin.json %>,     
                    <% include process/get-security-token.json %>,
                    <% include process/forgot-pin-confirm.json %>,
                    <% include process/nonces.json %>,
                    <% include process/save-device-info.json %>,
                    <% include process/third-party-login.json %>,
                    

                            {
                                    "responses": [ <% include support/proxy.ejs %> ]
                            }
                    ]
            }
    ]
}
