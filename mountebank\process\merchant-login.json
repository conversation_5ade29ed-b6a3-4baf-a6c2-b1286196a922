{"predicates": [{"and": [{"equals": {"method": "POST", "path": "/security-sys/merchant-login"}}, {"matches": {"body": {"msisdn": "^[a-zA-Z0-9\\d_*-.]*$", "password": "^[a-zA-Z0-9\\d_*-.+=/]*$"}}}]}], "responses": [{"is": {"statusCode": 200, "headers": {"Content-Type": "application/json", "wmt-mfs": "eyJhbGciOiJSU0ExXzUiLCJlbmMiOiJBMTI4Q0JDLUhTMjU2Iiwia2lkIjoibWZzIiwiY3R5IjoiSldUIn0.NQTtrkesxn7GjPn6VWYXTpJEDVb692xyCGgrNjd-W6bfGisHZ3q5DovDWbrfU8lskGoNtT_jcw8SrbYaAns30GkdUGKH6mGKjsZrI2_BFxj9_bZZvP8wM7xjMOdCR-PjyoPK-1vwru-qZ6vMpM2tZqJsMbnor1raCtKZlsffNw4L1aphTzLno9JXD4QKgWA_YntxYVgvzkChfPmSVFm4_ko2Tl-wmHqW8lWMHissMFzkSs0Onf8Rm7HCU-k9QJKIM1IjOl6k-m37eLdwWBqq7k1xlaFnch6Aq5Z7Yh4nGk07BYFb-N_he0YOb2iqwCHsuMywxoB3S1-QQF3UYxnRDw.pEG5INh2cuhLikp9BamWKA.K9J5-9N0WTcc8032V7qYmuZGXPw9V6tGMasmMi-IT62-itdvMr951l38sY8Av_ZiNeNqtn1mSeUN2NSBH0FW8IwbUuid-SlgYaEwFx-78sKB4AbZY7jEFn_IXrhmSyCvjbqhDMgb1j7_-PFBzBYuYEWW0JttYKye05lyAppScKqj40CzTfhVOb8td9S90j9B8j8YSajxNuKZWfOPW444ps9UcBTaYDHJhE0qumr5uvN_GQArJ9YWI_27H0jL0rFPoSIBI_NhcpnbomBzM-1XJJHgQm4cCHDpVnIz_oXldFuT8cTGXQHg8yasIq273nx0Y4_Y7d5rbrIJskNjudhbctl5o3o5bf750Vv0sveSSR-edRCMgZfVUcIPpjIwLMFz7bnps_O4TeoE-QPrXPoSHa8Gy5jhZQq8F1uu9IQGZYJ-qZXKP55xDummQoPPJn8oImfAR9t_xoweIs_fAufONoxavGLH003WlCAq1GA9n0Iaq7EXOG-eVuhxHiWa2Hp4rB1FQ-uaV0AkDAVx8PcjY_xf34VBJZ5qaet1kMfAyTN_giM9OKRYNOYiXtE9RH8PydhJvWD8skWXCklfCERYZ7Iyk7Vg0bS26IZgR1j7Jc7MU0xlAt3WGNkg9OXxWRoHySa6z_7FmBr3nTc2bDBjELsFlVJWorXb8cMEzqUIml8I7g9lksqX4Z0WY5vnX3zwD2dBLcTcDA5mCGyy3L2g4ne2C-yZ92149OCASk7W24prM-u8_5_PTgIdYcgN1q_AB2kK-Nxji6VJCPYNHJLD8-k3pMzK3SwrHTLDCMx-51wnz4gEQBqyafP4mUYh96iWI4Md1v-AakgigwZ8diM8DnQaUSFpLwxbOEbzYEYY6V85Uw71qvitaVPVV3URsqFU28FgEkUiK52FQO76xY261-uwEL-ANJS_hOeID6EaMc0FNbEeu09DmnjRg5Y4X01YMb00SSVhiEFLuB-7rJ7I8SLllf3cMprw3o7T5j2FsGrgg9_-f95NJmuAZX3TnsfHT3xSH23ga9ChnZoIYOClprNNR-0t8xRDRXiJXWZS6aJc3DsVIaIWBWJ2BFInCQQT5wxqugoSdKD6LIPTJZgWlVrKhLKs_dZ4orccOUNHUgvXxVoXc6YIwIioqRBvkkN5vxdPGqHnWCAjCukCjZttvnceTkgBcGOTbrBkISrEtO0NjyiOESUMlru422MDiuloaggk32GdhID5jYipow_OVngM8LaZQkxKDEyKw_g7URcvJlIYf44SStrJNl4rjk60oQT1e4Hm43yR1VxeRS9dQXtIiICSDkZZZDRMR-NhrbscHBbKpmnYFvhqLbAeRKNylqYbKWp5FPkcb-Ibq7xuS2ZZXXz7nwepLFXMTa1oY54tFK6CMz64BVhC4hOH2Iafd5ZE6051oU2iETSxE8lDYfduOPZvU7Mtx6kToo1IyzUBJQ4epRnqbnEoWBuVGgxp7zd2CgMfv6-E7hciOEO6EiKGqv8fJkXX0Ecce2A-bYdbq3s6SyS0YtmWaEm1GHQvwgtVD4vOFfROFPxXeLez5Wn1ZO2NHpuZaJstnMKa66c.7e5bPGnygjHWLTKKwGuNjA", "Vary": "Origin"}, "body": {"agentDetails": {"agentId": 97050312, "agentCategory": "merchant", "agentName": "Merchant Pyae Sone Lwin2", "agentGroup": "Micro_Merchant"}, "subscriberDetails": {"kycStatus": 2, "gender": "male", "dob": "1995-01-12", "name": "MerchantPyaeSone<PERSON><PERSON>", "msisdn": "9765403118", "nrc": "7/ThaNaPa(N)112332"}}}}]}