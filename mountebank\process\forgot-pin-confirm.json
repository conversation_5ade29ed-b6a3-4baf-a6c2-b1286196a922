{
    "predicates": [
        {
            "equals": {
                "method": "POST",
                "path": "/security-sys/forgot-pin-confirm",
                "headers": {
                    "content-type": "application/json"
                }
            }
        },
        {
            "matches": {
                "body": {
                    "msisdn": "9765444131",
                    "responseCode": "^(?![\\s]*$).*",
                    "responseData": "^(?![\\s]*$).*"
                }
            }
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "PIN_IS_RESET"
                    },
                    "message": "Your PIN has been reset",
                    "respTime": "Mar 25, 2025 02:03:47 PM"
                }
            }
        }
    ]
},
{
    "predicates": [
        {
            "equals": {
                "method": "POST",
                "path": "/security-sys/forgot-pin-confirm",
                "headers": {
                    "content-type": "application/json"
                }
            }
        },
        {
            "matches": {
                "body": {
                    "msisdn": "9409941398",
                    "responseCode": "^(?![\\s]*$).*",
                    "responseData": "^(?![\\s]*$).*"
                }
            }
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "DOB_INVALID"
                    },
                    "message": "Entered Date of birth does not match our records",
                    "respTime": "Dec 26, 2024 10:21:21 AM"
                }
            }
        }
    ]
},
{
    "predicates": [
        {
            "equals": {
                "method": "POST",
                "path": "/security-sys/forgot-pin-confirm",
                "headers": {
                    "content-type": "application/json"
                }
            }
        },
        {
            "matches": {
                "body": {
                    "msisdn": "9795566375",
                    "responseCode": "^(?![\\s]*$).*",
                    "responseData": "^(?![\\s]*$).*"
                }
            }
        }
    ],
    "responses": [
        {
            "is": {
                "statusCode": 200,
                "body": {
                    "responseMap": {
                        "code": "NRC_INVALID"
                    },
                    "message": "Entered NRC does not match our records",
                    "respTime": "Dec 26, 2024 02:48:42 PM"
                }
            }
        }
    ]
}