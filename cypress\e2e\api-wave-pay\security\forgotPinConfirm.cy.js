const ENDPOINTS = Cypress.env("endpoint");
const HEADERS = Cypress.env("headers");

describe("Forgot PIN Confirm", () => {
    it("should verify the forgot PIN successfully", () => {
        cy.fixture("forgotPinConfirm.json").then((forgotPinConfirm) => {
             cy.request({
                method: "POST",
                url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin-confirm`,
                headers: {
                    "content-type": "application/json",
                    "appid": HEADERS.appId,
                    "process-name": "forgot-pin-confirm",
                    "versioncode": HEADERS.versionCode,
                },
                body: forgotPinConfirm.forgotPinConfirmSuccess
             }).then((response) => {
                expect(response.status).to.equal(200);
                expect(response.body).to.have.property("responseMap");
                expect(response.body.responseMap).to.have.property("code", "PIN_IS_RESET");
                expect(response.body).to.have.property("message", "Your PIN has been reset");
                expect(response.body).to.have.property("respTime");
             });
        });
    });

    it("should verify the forgot PIN with invalid DOB", () => {
        cy.fixture("forgotPinConfirm.json").then((forgotPinConfirm) => {
            cy.request({
                method: "POST",
                url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin-confirm`,
                headers: {
                    "content-type": "application/json",
                    "appid": HEADERS.appId,
                    "process-name": "forgot-pin-confirm",
                    "versioncode": HEADERS.versionCode,
                },
                body: forgotPinConfirm.forgotPinConfirmDOBInvalid
            }).then((response) => {
                expect(response.status).to.equal(200);
                expect(response.body).to.have.property("responseMap");
                expect(response.body.responseMap).to.have.property("code", "DOB_INVALID");
                expect(response.body).to.have.property("message", "Entered Date of birth does not match our records");
                expect(response.body).to.have.property("respTime");
            });
        });
    }); 

    it("should verify the forgot PIN with invalid NRC", () => {
        cy.fixture("forgotPinConfirm.json").then((forgotPinConfirm) => {
            cy.request({
                method: "POST",
                url: `${ENDPOINTS.appUrl}/security-sys/forgot-pin-confirm`,
                headers: {
                    "content-type": "application/json",
                    "appid": HEADERS.appId,
                    "process-name": "forgot-pin-confirm",
                    "versioncode": HEADERS.versionCode,
                },
                body: forgotPinConfirm.forgotPinConfirmNRCInvalid
            }).then((response) => {
                expect(response.status).to.equal(200);
                expect(response.body).to.have.property("responseMap");
                expect(response.body.responseMap).to.have.property("code", "NRC_INVALID");
                expect(response.body).to.have.property("message", "Entered NRC does not match our records");
                expect(response.body).to.have.property("respTime");
            });
        });
    });
});

